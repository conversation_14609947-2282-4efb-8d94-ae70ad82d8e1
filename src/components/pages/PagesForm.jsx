'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import TextEditor from '@/components/common/TextEditor';
import LocationAndContactsInput from '@/components/pages/LocationAndContactsInput';
import BookingDetailsText from '@/components/pages/BookingDetailsText';
import TestimonialsInput from '@/components/pages/TestimonialsInput';
import { ActionButton } from '@/components/pages/ActionButtonGroup';

const PagesForm = React.memo(({ pages, onSave, onCancel, isLoading }) => {
  const [activeSection, setActiveSection] = useState('island');
  const [formData, setFormData] = useState({
    island: {
      title: '',
      image: '',
      body1: '',
      additionalContent: []
    },
    experiences: {
      title: '',
      image: '',
      body1: '',
      additionalContent: []
    },
    testimonials: {
      testimonials: []
    },
    locationAndcontacts: {
      title: '',
      body: '',
      details: ''
    },
    booking: {
      details: ''
    }
  });

  const [errors, setErrors] = useState({});
  const [uploadingImage, setUploadingImage] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);

  // Additional content form states
  const [showAdditionalContentForm, setShowAdditionalContentForm] = useState({
    island: false,
    experiences: false
  });
  const [additionalContentFormData, setAdditionalContentFormData] = useState({
    island: { image: '', title: '', body1: '' },
    experiences: { image: '', title: '', body1: '' }
  });
  const [additionalContentImageFile, setAdditionalContentImageFile] = useState({
    island: null,
    experiences: null
  });
  const [additionalContentImagePreview, setAdditionalContentImagePreview] = useState({
    island: '',
    experiences: ''
  });
  const [uploadingAdditionalContentImage, setUploadingAdditionalContentImage] = useState({
    island: false,
    experiences: false
  });

  // Edit mode state management for additional content
  const [editingAdditionalContent, setEditingAdditionalContent] = useState({
    island: null, // null or index of item being edited
    experiences: null
  });

  // Main image state management
  const [mainImageFile, setMainImageFile] = useState({
    island: null,
    experiences: null
  });
  const [mainImagePreview, setMainImagePreview] = useState({
    island: '',
    experiences: ''
  });
  const [uploadingMainImage, setUploadingMainImage] = useState({
    island: false,
    experiences: false
  });

  // Fetch fresh data for a specific section
  const fetchSectionData = useCallback(async (section) => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages');
      const data = await response.json();

      if (data.success) {
        const sectionData = data.data?.[section];
        if (sectionData) {
          setFormData(prev => ({
            ...prev,
            [section]: sectionData
          }));
        }
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error(`Error fetching latest ${section} data:`, error);
      setDataError(`Failed to load latest ${section} data. Using current values.`);
    } finally {
      setIsLoadingData(false);
    }
  }, []);

  // Initialize form data when pages prop changes
  useEffect(() => {
    if (pages) {
      setFormData({
        island: pages.island || {
          title: '',
          image: '',
          body1: '',
          additionalContent: []
        },
        experiences: pages.experiences || {
          title: '',
          image: '',
          body1: '',
          additionalContent: []
        },
        testimonials: pages.testimonials || {
          testimonials: []
        },
        locationAndcontacts: pages.locationAndcontacts || {
          title: '',
          body: '',
          details: ''
        },
        booking: pages.booking || {
          details: ''
        }
      });
    }
  }, [pages]);

  // Validation rules
  const validateForm = useCallback(() => {
    const newErrors = {};

    // Validate island section
    if (!formData.island.title?.trim()) {
      newErrors['island.title'] = 'Island title is required';
    }
    if (!formData.island.body1?.trim()) {
      newErrors['island.body1'] = 'Island body1 is required';
    }

    // Validate experiences section
    if (!formData.experiences.title?.trim()) {
      newErrors['experiences.title'] = 'Experiences title is required';
    }
    if (!formData.experiences.body1?.trim()) {
      newErrors['experiences.body1'] = 'Experiences body1 is required';
    }

    // Validate location and contacts section
    if (!formData.locationAndcontacts.title?.trim()) {
      newErrors['locationAndcontacts.title'] = 'Location title is required';
    }
    if (!formData.locationAndcontacts.body?.trim()) {
      newErrors['locationAndcontacts.body'] = 'Location body is required';
    }
    if (!formData.locationAndcontacts.details?.trim()) {
      newErrors['locationAndcontacts.details'] = 'Location details are required';
    }

    // Validate booking section
    if (!formData.booking.details?.trim()) {
      newErrors['booking.details'] = 'Booking details are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle Quill editor content changes
  const handleQuillChange = useCallback((content, section, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [fieldName]: content
      }
    }));

    // Clear error when user starts typing
    if (errors[`${section}.${fieldName}`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}.${fieldName}`]: ''
      }));
    }
  }, [errors]);

  // Handle regular field changes
  const handleFieldChange = useCallback((section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));

    // Clear error when user starts typing
    if (errors[`${section}.${field}`]) {
      setErrors(prev => ({
        ...prev,
        [`${section}.${field}`]: ''
      }));
    }
  }, [errors]);

  // Handle image upload
  const handleImageUpload = useCallback(async (file, section) => {
    setUploadingImage(true);

    try {
      const uploadFormData = new FormData();
      uploadFormData.append('files', file);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: uploadFormData,
      });

      const result = await response.json();

      if (result.success && result.files?.[0]) {
        const imageUrl = result.files[0].url;
        handleFieldChange(section, 'image', imageUrl);
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setErrors(prev => ({
        ...prev,
        [`${section}.image`]: 'Failed to upload image'
      }));
    } finally {
      setUploadingImage(false);
    }
  }, [handleFieldChange]);



  // Additional content handlers
  const handleAdditionalContentImageChange = useCallback((e, section) => {
    const file = e.target.files[0];
    if (file) {
      setAdditionalContentImageFile(prev => ({ ...prev, [section]: file }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setAdditionalContentImagePreview(prev => ({ ...prev, [section]: e.target.result }));
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleAdditionalContentFormChange = useCallback((field, value, section) => {
    setAdditionalContentFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  }, []);

  const uploadAdditionalContentImage = useCallback(async (section) => {
    const file = additionalContentImageFile[section];
    if (!file) return null;

    setUploadingAdditionalContentImage(prev => ({ ...prev, [section]: true }));

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success && data.files && data.files.length > 0) {
        return data.files[0].url;
      } else {
        throw new Error(data.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setUploadingAdditionalContentImage(prev => ({ ...prev, [section]: false }));
    }
  }, [additionalContentImageFile]);

  const handleSaveAdditionalContent = useCallback(async (section) => {
    try {
      const formData = additionalContentFormData[section];

      // Validate required fields
      if (!formData.title || !formData.body1) {
        setErrors(prev => ({
          ...prev,
          [`${section}.additionalContent`]: 'All fields are required'
        }));
        return;
      }

      if (!additionalContentImageFile[section] && !formData.image) {
        setErrors(prev => ({
          ...prev,
          [`${section}.additionalContent`]: 'Image is required'
        }));
        return;
      }

      let imageUrl = formData.image;

      // Upload new image if selected
      if (additionalContentImageFile[section]) {
        imageUrl = await uploadAdditionalContentImage(section);
      }

      const itemData = {
        image: imageUrl,
        title: formData.title,
        body1: formData.body1
      };

      const editIndex = editingAdditionalContent[section];

      // Update or add item based on edit mode
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          additionalContent: editIndex !== null
            ? prev[section].additionalContent.map((item, index) =>
                index === editIndex ? itemData : item
              )
            : [...prev[section].additionalContent, itemData]
        }
      }));

      // Reset form and edit state
      setAdditionalContentFormData(prev => ({
        ...prev,
        [section]: { image: '', title: '', body1: '' }
      }));
      setAdditionalContentImageFile(prev => ({ ...prev, [section]: null }));
      setAdditionalContentImagePreview(prev => ({ ...prev, [section]: '' }));
      setShowAdditionalContentForm(prev => ({ ...prev, [section]: false }));
      setEditingAdditionalContent(prev => ({ ...prev, [section]: null }));

      // Clear any errors
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${section}.additionalContent`];
        return newErrors;
      });

    } catch (error) {
      console.error('Save additional content error:', error);
      setErrors(prev => ({
        ...prev,
        [`${section}.additionalContent`]: 'Failed to save additional content: ' + error.message
      }));
    }
  }, [additionalContentFormData, additionalContentImageFile, uploadAdditionalContentImage]);

  const handleRemoveAdditionalContent = useCallback((section, index) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        additionalContent: prev[section].additionalContent.filter((_, i) => i !== index)
      }
    }));
  }, []);

  // Edit handlers for additional content
  const handleEditAdditionalContent = useCallback((section, index) => {
    const item = formData[section].additionalContent[index];

    // Populate form with existing data
    setAdditionalContentFormData(prev => ({
      ...prev,
      [section]: {
        image: item.image,
        title: item.title,
        body1: item.body1
      }
    }));

    // Set edit mode
    setEditingAdditionalContent(prev => ({ ...prev, [section]: index }));
    setShowAdditionalContentForm(prev => ({ ...prev, [section]: true }));

    // Clear any existing image file/preview since we're editing
    setAdditionalContentImageFile(prev => ({ ...prev, [section]: null }));
    setAdditionalContentImagePreview(prev => ({ ...prev, [section]: '' }));
  }, [formData]);

  const handleCancelEditAdditionalContent = useCallback((section) => {
    // Reset form and exit edit mode
    setAdditionalContentFormData(prev => ({
      ...prev,
      [section]: { image: '', title: '', body1: '' }
    }));
    setAdditionalContentImageFile(prev => ({ ...prev, [section]: null }));
    setAdditionalContentImagePreview(prev => ({ ...prev, [section]: '' }));
    setShowAdditionalContentForm(prev => ({ ...prev, [section]: false }));
    setEditingAdditionalContent(prev => ({ ...prev, [section]: null }));

    // Clear any errors
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`${section}.additionalContent`];
      return newErrors;
    });
  }, []);

  // Main image handlers
  const handleMainImageChange = useCallback((e, section) => {
    const file = e.target.files[0];
    if (file) {
      setMainImageFile(prev => ({ ...prev, [section]: file }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setMainImagePreview(prev => ({ ...prev, [section]: e.target.result }));
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const uploadMainImage = useCallback(async (section) => {
    const file = mainImageFile[section];
    if (!file) return null;

    setUploadingMainImage(prev => ({ ...prev, [section]: true }));

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload/pages', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success && data.files && data.files.length > 0) {
        return data.files[0].url;
      } else {
        throw new Error(data.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Main image upload error:', error);
      throw error;
    } finally {
      setUploadingMainImage(prev => ({ ...prev, [section]: false }));
    }
  }, [mainImageFile]);

  const handleMainImageSave = useCallback(async (section) => {
    try {
      if (!mainImageFile[section]) {
        setErrors(prev => ({
          ...prev,
          [`${section}.image`]: 'Please select an image to upload'
        }));
        return;
      }

      const imageUrl = await uploadMainImage(section);

      // Update form data with the new image URL
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          image: imageUrl
        }
      }));

      // Reset image upload state
      setMainImageFile(prev => ({ ...prev, [section]: null }));
      setMainImagePreview(prev => ({ ...prev, [section]: '' }));

      // Clear any errors
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${section}.image`];
        return newErrors;
      });

    } catch (error) {
      console.error('Save main image error:', error);
      setErrors(prev => ({
        ...prev,
        [`${section}.image`]: 'Failed to upload image: ' + error.message
      }));
    }
  }, [mainImageFile, uploadMainImage]);

  const handleRemoveMainImage = useCallback((section) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        image: ''
      }
    }));
    setMainImageFile(prev => ({ ...prev, [section]: null }));
    setMainImagePreview(prev => ({ ...prev, [section]: '' }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Save error:', error);
    }
  }, [formData, validateForm, onSave]);

  // Handle section save
  const handleSectionSave = useCallback(async (section, sectionData = null) => {
    if (!validateForm()) {
      return;
    }

    try {
      if (section) {
        // Use provided section data or fall back to formData
        const dataToSave = sectionData || formData[section];
        // Save specific section
        await onSave({ section, data: dataToSave });
      } else {
        // Save entire form
        await onSave(formData);
      }
    } catch (error) {
      console.error('Save error:', error);
      throw error;
    }
  }, [formData, validateForm, onSave]);

  // Memoized section options
  const sectionOptions = useMemo(() => [
    { value: 'island', label: 'The Island' },
    { value: 'experiences', label: 'Experiences' },
    { value: 'testimonials', label: 'Testimonials' },
    { value: 'locationAndcontacts', label: 'Location & Contacts' },
    { value: 'booking', label: 'Booking' }
  ], []);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-trasandina-black text-gray-900 uppercase tracking-wide">
          Pages Management
        </h2>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="pages-form"
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save All Pages'}
          </button>
        </div>
      </div>

      {/* Section Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {sectionOptions.map((section) => (
            <button
              key={section.value}
              type="button"
              onClick={() => setActiveSection(section.value)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeSection === section.value
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      <form id="pages-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Island Section */}
        {activeSection === 'island' && (
          <IslandSection
            formData={formData.island}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('island')}
            isLoading={isLoading}
            isLoadingData={isLoadingData}
            dataError={dataError}
            fetchSectionData={fetchSectionData}
            showAdditionalContentForm={showAdditionalContentForm}
            setShowAdditionalContentForm={setShowAdditionalContentForm}
            additionalContentFormData={additionalContentFormData}
            handleAdditionalContentFormChange={handleAdditionalContentFormChange}
            additionalContentImagePreview={additionalContentImagePreview}
            handleAdditionalContentImageChange={handleAdditionalContentImageChange}
            uploadingAdditionalContentImage={uploadingAdditionalContentImage}
            handleSaveAdditionalContent={handleSaveAdditionalContent}
            handleRemoveAdditionalContent={handleRemoveAdditionalContent}
            editingAdditionalContent={editingAdditionalContent}
            handleEditAdditionalContent={handleEditAdditionalContent}
            handleCancelEditAdditionalContent={handleCancelEditAdditionalContent}
            mainImageFile={mainImageFile}
            mainImagePreview={mainImagePreview}
            handleMainImageChange={handleMainImageChange}
            uploadingMainImage={uploadingMainImage}
            handleMainImageSave={handleMainImageSave}
            handleRemoveMainImage={handleRemoveMainImage}
          />
        )}

        {/* Experiences Section */}
        {activeSection === 'experiences' && (
          <ExperiencesSection
            formData={formData.experiences}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('experiences')}
            isLoading={isLoading}
            isLoadingData={isLoadingData}
            dataError={dataError}
            fetchSectionData={fetchSectionData}
            showAdditionalContentForm={showAdditionalContentForm}
            setShowAdditionalContentForm={setShowAdditionalContentForm}
            additionalContentFormData={additionalContentFormData}
            handleAdditionalContentFormChange={handleAdditionalContentFormChange}
            additionalContentImagePreview={additionalContentImagePreview}
            handleAdditionalContentImageChange={handleAdditionalContentImageChange}
            uploadingAdditionalContentImage={uploadingAdditionalContentImage}
            handleSaveAdditionalContent={handleSaveAdditionalContent}
            handleRemoveAdditionalContent={handleRemoveAdditionalContent}
            editingAdditionalContent={editingAdditionalContent}
            handleEditAdditionalContent={handleEditAdditionalContent}
            handleCancelEditAdditionalContent={handleCancelEditAdditionalContent}
            mainImageFile={mainImageFile}
            mainImagePreview={mainImagePreview}
            handleMainImageChange={handleMainImageChange}
            uploadingMainImage={uploadingMainImage}
            handleMainImageSave={handleMainImageSave}
            handleRemoveMainImage={handleRemoveMainImage}
          />
        )}

        {/* Testimonials Section */}
        {activeSection === 'testimonials' && (
          <TestimonialsInput
            formData={formData.testimonials}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('testimonials')}
            isLoading={isLoading}
          />
        )}

        {/* Location and Contacts Section */}
        {activeSection === 'locationAndcontacts' && (
          <LocationAndContactsInput
            formData={formData.locationAndcontacts}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={() => handleSectionSave('locationAndcontacts')}
            isLoading={isLoading}
          />
        )}

        {/* Booking Section */}
        {activeSection === 'booking' && (
          <BookingDetailsText
            formData={formData.booking}
            errors={errors}
            onQuillChange={handleQuillChange}
            onSectionSave={(section, sectionData) => handleSectionSave(section, sectionData)}
            isLoading={isLoading}
          />
        )}
      </form>
    </div>
  );
});

// Additional Content Section Component
const AdditionalContentSection = React.memo(({
  section,
  formData,
  showForm,
  setShowForm,
  additionalContentFormData,
  onFormChange,
  imagePreview,
  onImageChange,
  uploading,
  onSave,
  onRemove,
  errors,
  editingIndex,
  onEdit,
  onCancelEdit
}) => (
  <div className="space-y-4">
    <div className="border-t border-gray-200 pt-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-md font-medium text-gray-900">Additional Content</h4>
        <button
          type="button"
          onClick={() => {
            if (showForm && editingIndex !== null) {
              onCancelEdit();
            } else {
              setShowForm(!showForm);
            }
          }}
          className="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {showForm
            ? (editingIndex !== null ? 'Cancel Edit' : 'Cancel')
            : 'Add Additional Content'
          }
        </button>
      </div>

      {/* Display existing additional content */}
      {formData.additionalContent && formData.additionalContent.length > 0 && (
        <div className="space-y-3 mb-4">
          <h5 className="text-sm font-medium text-gray-700">Current Additional Content:</h5>
          {formData.additionalContent.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-3">
                {item.image && (
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-12 h-12 object-cover rounded"
                  />
                )}
                <div>
                  <p className="text-sm font-medium text-gray-900" dangerouslySetInnerHTML={{ __html: item.title }} />
                  <p className="text-xs text-gray-500">Content item {index + 1}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => onEdit(index)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Edit
                </button>
                <button
                  type="button"
                  onClick={() => onRemove(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Additional content form */}
      {showForm && (
        <div className="space-y-4 p-4 bg-gray-50 rounded-md">
          <h5 className="text-sm font-medium text-gray-700">Add New Content</h5>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image *
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={onImageChange}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
            {imagePreview && (
              <div className="mt-2">
                <img
                  src={imagePreview}
                  alt="Preview"
                  className="w-20 h-20 object-cover rounded"
                />
              </div>
            )}
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <TextEditor
              value={additionalContentFormData.title}
              onChange={(content) => onFormChange('title', content)}
              placeholder="Enter content title"
              style={{ minHeight: '60px' }}
              className="border rounded-md border-gray-300"
            />
          </div>

          {/* Body 1 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Body 1 *
            </label>
            <TextEditor
              value={additionalContentFormData.body1}
              onChange={(content) => onFormChange('body1', content)}
              placeholder="Enter first body content"
              style={{ minHeight: '100px' }}
              className="border rounded-md border-gray-300"
            />
          </div>

          {/* Error display */}
          {errors[`${section}.additionalContent`] && (
            <p className="text-sm text-red-600">{errors[`${section}.additionalContent`]}</p>
          )}

          {/* Save button */}
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onSave}
              disabled={uploading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading
                ? (editingIndex !== null ? 'Updating...' : 'Saving...')
                : (editingIndex !== null ? 'Update Content' : 'Save Content')
              }
            </button>
          </div>
        </div>
      )}
    </div>
  </div>
));

// Main Image Upload Section Component
const MainImageUploadSection = React.memo(({
  section,
  formData,
  imageFile,
  imagePreview,
  onImageChange,
  uploading,
  onSave,
  onRemove,
  errors
}) => (
  <div className="space-y-4">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Main Image
      </label>

      {/* Current Image Display */}
      {formData.image && !imagePreview && (
        <div className="mb-3">
          <div className="flex items-center space-x-3">
            <img
              src={formData.image}
              alt="Current main image"
              className="w-20 h-20 object-cover rounded border"
            />
            <div className="flex-1">
              <p className="text-sm text-gray-600">Current main image</p>
              <button
                type="button"
                onClick={onRemove}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Remove Image
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Upload Input */}
      <div className="flex items-center space-x-3">
        <input
          type="file"
          accept="image/*"
          onChange={onImageChange}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        {imageFile && (
          <button
            type="button"
            onClick={onSave}
            disabled={uploading}
            className="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {uploading ? 'Uploading...' : 'Upload Image'}
          </button>
        )}
      </div>

      {/* Image Preview */}
      {imagePreview && (
        <div className="mt-3">
          <p className="text-sm text-gray-600 mb-2">Preview:</p>
          <img
            src={imagePreview}
            alt="Preview"
            className="w-32 h-32 object-cover rounded border"
          />
        </div>
      )}

      {/* Error Display */}
      {errors[`${section}.image`] && (
        <p className="mt-1 text-sm text-red-600">{errors[`${section}.image`]}</p>
      )}
    </div>
  </div>
));

// Island Section Component
const IslandSection = React.memo(({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading,
  isLoadingData,
  dataError,
  fetchSectionData,
  showAdditionalContentForm,
  setShowAdditionalContentForm,
  additionalContentFormData,
  handleAdditionalContentFormChange,
  additionalContentImagePreview,
  handleAdditionalContentImageChange,
  uploadingAdditionalContentImage,
  handleSaveAdditionalContent,
  handleRemoveAdditionalContent,
  editingAdditionalContent,
  handleEditAdditionalContent,
  handleCancelEditAdditionalContent,
  mainImageFile,
  mainImagePreview,
  handleMainImageChange,
  uploadingMainImage,
  handleMainImageSave,
  handleRemoveMainImage
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Island Page Content</h3>
      <div className="flex items-center gap-2">
        <ActionButton
          variant="outlinedPrimary"
          onClick={() => fetchSectionData('island')}
          disabled={isLoadingData}
          loading={isLoadingData}
          loadingText="Refreshing..."
        >
          {isLoadingData ? 'Refreshing...' : 'Refresh Data'}
        </ActionButton>
        <ActionButton
          variant="success"
          onClick={onSectionSave}
          disabled={isLoading}
          loading={isLoading}
          loadingText="Saving..."
        >
          {isLoading ? 'Saving...' : 'Save Island Section'}
        </ActionButton>
      </div>
    </div>

    {/* Data Error Display */}
    {dataError && (
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-800">{dataError}</p>
          </div>
        </div>
      </div>
    )}

    {/* Loading State */}
    {isLoadingData && (
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
          <p className="text-sm text-blue-800">Loading latest island data...</p>
        </div>
      </div>
    )}

    {/* Title */}
    <div>
      <label htmlFor="island-title" className="block text-sm font-medium text-gray-700 mb-2">
        Title *
      </label>
      <div className={`${errors['island.title'] ? 'border-red-500' : ''}`}>
        <TextEditor
          value={formData.title}
          onChange={(content) => onQuillChange(content, 'island', 'title')}
          placeholder="Enter island page title"
          style={{ minHeight: '80px' }}
          className={`border rounded-md ${errors['island.title'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['island.title'] && (
        <p className="mt-1 text-sm text-red-600">{errors['island.title']}</p>
      )}
    </div>

    {/* Main Image */}
    <MainImageUploadSection
      section="island"
      formData={formData}
      imageFile={mainImageFile.island}
      imagePreview={mainImagePreview.island}
      onImageChange={(e) => handleMainImageChange(e, 'island')}
      uploading={uploadingMainImage.island}
      onSave={() => handleMainImageSave('island')}
      onRemove={() => handleRemoveMainImage('island')}
      errors={errors}
    />

    {/* Body 1 */}
    <div>
      <label htmlFor="island-body1" className="block text-sm font-medium text-gray-700 mb-2">
        Body 1 *
      </label>
      <div className={`${errors['island.body1'] ? 'border-red-500' : ''}`}>
        <TextEditor
          value={formData.body1}
          onChange={(content) => onQuillChange(content, 'island', 'body1')}
          placeholder="Enter first body content"
          style={{ minHeight: '120px' }}
          className={`border rounded-md ${errors['island.body1'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['island.body1'] && (
        <p className="mt-1 text-sm text-red-600">{errors['island.body1']}</p>
      )}
    </div>

    {/* Additional Content Section */}
    <AdditionalContentSection
      section="island"
      formData={formData}
      showForm={showAdditionalContentForm.island}
      setShowForm={(show) => setShowAdditionalContentForm(prev => ({ ...prev, island: show }))}
      additionalContentFormData={additionalContentFormData.island}
      onFormChange={(field, value) => handleAdditionalContentFormChange(field, value, 'island')}
      imagePreview={additionalContentImagePreview.island}
      onImageChange={(e) => handleAdditionalContentImageChange(e, 'island')}
      uploading={uploadingAdditionalContentImage.island}
      onSave={() => handleSaveAdditionalContent('island')}
      onRemove={(index) => handleRemoveAdditionalContent('island', index)}
      errors={errors}
      editingIndex={editingAdditionalContent.island}
      onEdit={(index) => handleEditAdditionalContent('island', index)}
      onCancelEdit={() => handleCancelEditAdditionalContent('island')}
    />
  </div>
));

// Experiences Section Component
const ExperiencesSection = React.memo(({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading,
  isLoadingData,
  dataError,
  fetchSectionData,
  showAdditionalContentForm,
  setShowAdditionalContentForm,
  additionalContentFormData,
  handleAdditionalContentFormChange,
  additionalContentImagePreview,
  handleAdditionalContentImageChange,
  uploadingAdditionalContentImage,
  handleSaveAdditionalContent,
  handleRemoveAdditionalContent,
  editingAdditionalContent,
  handleEditAdditionalContent,
  handleCancelEditAdditionalContent,
  mainImageFile,
  mainImagePreview,
  handleMainImageChange,
  uploadingMainImage,
  handleMainImageSave,
  handleRemoveMainImage
}) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-medium text-gray-900">Experiences Page Content</h3>
      <div className="flex items-center gap-2">
        <ActionButton
          variant="outlinedPrimary"
          onClick={() => fetchSectionData('experiences')}
          disabled={isLoadingData}
          loading={isLoadingData}
          loadingText="Refreshing..."
        >
          {isLoadingData ? 'Refreshing...' : 'Refresh Data'}
        </ActionButton>
        <ActionButton
          variant="success"
          onClick={onSectionSave}
          disabled={isLoading}
          loading={isLoading}
          loadingText="Saving..."
        >
          {isLoading ? 'Saving...' : 'Save Experiences Section'}
        </ActionButton>
      </div>
    </div>

    {/* Data Error Display */}
    {dataError && (
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-800">{dataError}</p>
          </div>
        </div>
      </div>
    )}

    {/* Loading State */}
    {isLoadingData && (
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
          <p className="text-sm text-blue-800">Loading latest experiences data...</p>
        </div>
      </div>
    )}

    {/* Title */}
    <div>
      <label htmlFor="experiences-title" className="block text-sm font-medium text-gray-700 mb-2">
        Title *
      </label>
      <div className={`${errors['experiences.title'] ? 'border-red-500' : ''}`}>
        <TextEditor
          value={formData.title}
          onChange={(content) => onQuillChange(content, 'experiences', 'title')}
          placeholder="Enter experiences page title"
          style={{ minHeight: '80px' }}
          className={`border rounded-md ${errors['experiences.title'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['experiences.title'] && (
        <p className="mt-1 text-sm text-red-600">{errors['experiences.title']}</p>
      )}
    </div>

    {/* Main Image */}
    <MainImageUploadSection
      section="experiences"
      formData={formData}
      imageFile={mainImageFile.experiences}
      imagePreview={mainImagePreview.experiences}
      onImageChange={(e) => handleMainImageChange(e, 'experiences')}
      uploading={uploadingMainImage.experiences}
      onSave={() => handleMainImageSave('experiences')}
      onRemove={() => handleRemoveMainImage('experiences')}
      errors={errors}
    />

    {/* Body 1 */}
    <div>
      <label htmlFor="experiences-body1" className="block text-sm font-medium text-gray-700 mb-2">
        Body 1 *
      </label>
      <div className={`${errors['experiences.body1'] ? 'border-red-500' : ''}`}>
        <TextEditor
          value={formData.body1}
          onChange={(content) => onQuillChange(content, 'experiences', 'body1')}
          placeholder="Enter first body content"
          style={{ minHeight: '120px' }}
          className={`border rounded-md ${errors['experiences.body1'] ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {errors['experiences.body1'] && (
        <p className="mt-1 text-sm text-red-600">{errors['experiences.body1']}</p>
      )}
    </div>

    {/* Additional Content Section */}
    <AdditionalContentSection
      section="experiences"
      formData={formData}
      showForm={showAdditionalContentForm.experiences}
      setShowForm={(show) => setShowAdditionalContentForm(prev => ({ ...prev, experiences: show }))}
      additionalContentFormData={additionalContentFormData.experiences}
      onFormChange={(field, value) => handleAdditionalContentFormChange(field, value, 'experiences')}
      imagePreview={additionalContentImagePreview.experiences}
      onImageChange={(e) => handleAdditionalContentImageChange(e, 'experiences')}
      uploading={uploadingAdditionalContentImage.experiences}
      onSave={() => handleSaveAdditionalContent('experiences')}
      onRemove={(index) => handleRemoveAdditionalContent('experiences', index)}
      errors={errors}
      editingIndex={editingAdditionalContent.experiences}
      onEdit={(index) => handleEditAdditionalContent('experiences', index)}
      onCancelEdit={() => handleCancelEditAdditionalContent('experiences')}
    />
  </div>
));






// Set display names
IslandSection.displayName = 'IslandSection';
ExperiencesSection.displayName = 'ExperiencesSection';
PagesForm.displayName = 'PagesForm';

export default PagesForm;
