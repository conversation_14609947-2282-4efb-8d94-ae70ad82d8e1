import EntranceMarker from "@/components/landingpage/EntranceMarker";
import Image from "next/image";

export const settings={
    markerList:{
        markerTypeIcons:{
            landingPage:{
                btnIcons:{off:<EntranceMarker/>},
                width:120,
                height:120,
                type:'guide'
            },
            enterButton:{
                btnIcons:{
                    off:'/assets/step_inside_btn_off.png',
                    ov:'/assets/step_inside_btn_ov.png',
                },
                width:60,
                height:60,
                type:'guide'
            },
            guide:{
                btnIcons:{
                    off:'/assets/guide_btn_off.png',
                    ov:'/assets/guide_btn_ov.png',
                },
                width:60,
                height:60,
                type:'guide'
            },
            upstairs:{
                btnIcons:{
                    off:'/assets/upstairs_btn_off.png',
                    ov:'/assets/upstairs_btn_ov.png',
                },
                width:60,
                height:60,
                type:'guide'
            },
            downstairs:{
                btnIcons:{
                    off:'/assets/downstairs_btn_off.png',
                    ov:'/assets/downstairs_btn_ov.png',
                },
                width:60,
                height:60,
                type:'guide'
            },
            infoDoc:{btnIcons:{
                    off:'/assets/read_more_btn_off.png',
                    ov:'/assets/read_more_btn_ov.png',
                },
                width:60,
                height:60,
                type:'content'
            },
            infoImage:{
                btnIcons:{
                    off:'/assets/photo_btn_off.png',
                    ov:'/assets/photo_btn_ov.png',
                },
                width:60,
                height:60,
                type:'content'
            },
            infoVideo:{
                btnIcons:{
                    off:'/assets/video_btn_off.png',
                    ov:'/assets/video_btn_ov.png',
                },
                width:60,
                height:60,
                type:'content'
            },
        },
        markerType:[
            'landingPage',
            'guide',
            'upstairs',
            'downstairs',
            'infoVideo',
            'infoDoc',
            'infoImage',
        ],
        locationsList:[
            'entrance',
            'first floor',
            'the outdoors',
        ],
        markerTarget:[
            'snappoint',
            'content',
        ],
        contentType:[
            'video',
            'infoDoc',
            'gallery',
        ]
    },
    sixdotsLogo:'/assets/six_dots_studios_logo.png',
    luyariLogo:'/assets/luyari_logo.png',
    socials:[
        {name:'facebook',off:'/assets/facebook_btn_off.png',ov:'/assets/facebook_btn_ov.png'},
        {name:'youtube',off:'/assets/youtube_btn_off.png',ov:'/assets/youtube_btn_ov.png'},
        {name:'sound',off:'/assets/sound_btn_off.png',ov:'/assets/sound_btn_on.png'},
    ],
    landingPage:{
        logo:'/assets/elephant_island_logo_updated.png',
        list:[
            {
                name:'individuals',
                btnIcons:{
                    ov:'/assets/individuals_btn_ov.png',
                    off:'/assets/individuals_btn_off.png',
                },
                width:97,
                height:94,
            },
            {
                name:'families',
                btnIcons:{
                    ov:'/assets/families_btn_ov.png',
                    off:'/assets/families_btn_off.png',
                },
                width:128,
                height:94,
            },
            {
                name:'couples',
                btnIcons:{
                    ov:'/assets/couples_btn_ov.png',
                    off:'/assets/couples_btn_off.png',
                },
                width:92,
                height:94,
            },
        ],
        btns:[
            {   
                name:'explore',
                btnIcons:{
                    ov:'/assets/explore_btn_ov.png',
                    off:'/assets/explore_btn_off.png',
                },
                width:120,
                height:46,
            },
            {
                name:'book now',
                btnIcons:{
                    ov:'/assets/book_now_btn_ov.png',
                    off:'/assets/book_now_btn_off.png',
                },
                width:129,
                height:46,
            },
        ],
    },
    landingPage360:{
        logo:'/assets/elephant_island_logo_001.png',
        logoMobile:'/assets/mobile_elephant_island_logo.png',
        LandingPageGuide:'/assets/look_around_and_explore.png',
        LandingPageGuideMobile:'/assets/mobile_look_around_and_explore.png',
        enterBtn:{
            name:'individuals',
            btnIcons:{
                ov:'/assets/enter_btn_ov.png',
                off:'/assets/enter_btn_off.png',
            },
            width:97,
            height:94,
        },
        enterBtnMobile:{
            name:'individuals',
            btnIcons:{
                ov:'/assets/mobile_enter_360_btn_ov.png',
                off:'/assets/mobile_enter_360_btn_off.png',
            },
            width:97,
            height:94,
        },
        list:[
            {
                name:'families',
                btnIcons:{
                    ov:'/assets/families_btn_ov.png',
                    off:'/assets/families_btn_off.png',
                },
                width:128,
                height:94,
            },
            {
                name:'couples',
                btnIcons:{
                    ov:'/assets/couples_btn_ov.png',
                    off:'/assets/couples_btn_off.png',
                },
                width:92,
                height:94,
            },
        ],
        btns:[
            {   
                name:'explore',
                btnIcons:{
                    ov:'/assets/explore_btn_ov.png',
                    off:'/assets/explore_btn_off.png',
                },
                width:120,
                height:46,
            },
            {
                name:'book now',
                btnIcons:{
                    ov:'/assets/book_now_btn_ov.png',
                    off:'/assets/book_now_btn_off.png',
                },
                width:129,
                height:46,
            },
        ],
    },
    videos:[    
        '/assets/video/360_Drone_Reverse.mp4',
    ],
    locatioSubmitBtn:{
        name:'submit',
        btnIcons:{
            off:'/assets/send_message_btn_off.png',
            ov:'/assets/send_message_btn_ov.png',
        },
        width:97,
        height:94,
    },
    siteName:{name:'elephantislandbotswana',maxim:"experince your world"},
    url:process.env.NODE_ENV=='production' ? 'https://elephantislandbotswana.com' : 'https://localhost:3003',
    menuPopup:{
        home:[
            {
                name:'home',
                btnIcons:{
                    off:'/assets/home_btn_off.png',
                    ov:'/assets/home_btn_ov.png',
                },
                width:97,
                height:94,
            },
        ],
        entrance:[
            {
                location:'entrance',
                name:'entrance_360',
                btnIcons:{
                    off:'/assets/entrance_btn_off.png',
                    ov:'/assets/entrance_btn_ov.png',
                },
                width:142,
                height:46,
            },
            {
                location:'entrance',
                name:'livingroom_001',
                btnIcons:{
                    off:'/assets/lounge_btn_off.png',
                    ov:'/assets/lounge_btn_ov.png',
                },
                width:119,
                height:39,
            },
            {
                location:'entrance',
                name:'dinig_001',
                btnIcons:{
                    off:'/assets/dining_btn_off.png',
                    ov:'/assets/dining_btn_ov.png',
                },
                width:119,
                height:39,
            },
        ],
        firstFloor:[
            {
                location:'first floor',
                name:'first first',
                btnIcons:{
                    off:'/assets/first_floor_btn_off.png',
                    ov:'/assets/first_floor_btn_ov.png',
                },
                width:160,
                height:46,
            },
            {
                location:'first floor',
                name:'bedroom 1',
                btnIcons:{
                    off:'/assets/bedroom_1_btn_off.png',
                    ov:'/assets/bedroom_1_btn_ov.png',
                },
                width:149,
                height:39,
            },
            {
                location:'first floor',
                name:'bedroom 2',
                btnIcons:{
                    off:'/assets/bedroom_2_btn_off.png',
                    ov:'/assets/bedroom_2_btn_ov.png',
                },
                width:149,
                height:39,
            },
            {
                location:'first floor',
                name:'master bedroom',
                btnIcons:{
                    off:'/assets/master_bedroom_btn_off.png',
                    ov:'/assets/master_bedroom_btn_ov.png',
                },
                width:204,
                height:39,
            },
            {
                location:'first floor',
                name:'balcony',
                btnIcons:{
                    off:'/assets/balcony_btn_off.png',
                    ov:'/assets/balcony_btn_ov.png',
                },
                width:130,
                height:39,
            },
        ],
        outDoors:[
            {
                location:'the outdoors',
                name:'the outdoors',
                btnIcons:{
                    off:'/assets/the_outdoors_btn_off.png',
                    ov:'/assets/the_outdoors_btn_ov.png',
                },
                width:190,
                height:46,
            },
            {
                location:'the outdoors',
                name:'terrace',
                btnIcons:{
                    off:'/assets/terrace_btn_off.png',
                    ov:'/assets/terrace_btn_ov.png',
                },
                width:128,
                height:39,
            },
            {
                location:'the outdoors',
                name:'west view',
                btnIcons:{
                    off:'/assets/west_view_btn_off.png',
                    ov:'/assets/west_view_btn_ov.png',
                },
                width:150,
                height:39,
            },
            {
                location:'the outdoors',
                name:'east view',
                btnIcons:{
                    off:'/assets/east_view_btn_off.png',
                    ov:'/assets/east_view_btn_ov.png',
                },
                width:150,
                height:39,
            },
            {
                location:'the outdoors',
                name:'south view',
                btnIcons:{
                    off:'/assets/south_view_btn_off.png',
                    ov:'/assets/south_view_btn_ov.png',
                },
                width:150,
                height:39,
            },
            {
                location:'the outdoors',
                name:'north view',
                btnIcons:{
                    off:'/assets/north_view_btn_off.png',
                    ov:'/assets/north_view_btn_ov.png',
                },
                width:159,
                height:39,
            },
        ],
        campOutskirts:[
            {
                location:'the outdoors',
                name:'the outdoors',
                btnIcons:{
                    off:'/assets/camp_site_btn_off.png',
                    ov:'/assets/camp_site_btn_ov.png',
                },
                width:190,
                height:46,
            },
        ],
    },
}